import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/models/entities/contract/apply_trial_contract_config_entity.dart';
import 'package:gp_stock_app/core/models/entities/contract/contract.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/contract/domain/models/contract_model.dart';
import 'package:gp_stock_app/features/contract/widgets/amount_row.dart';
import 'package:gp_stock_app/features/contract/widgets/app_info_bottom_sheet.dart';
import 'package:gp_stock_app/features/contract/widgets/future_funding_widget.dart';
import 'package:gp_stock_app/features/contract/widgets/selectable_button.dart';
import 'package:gp_stock_app/shared/app/extension/color_extension.dart';
import 'package:gp_stock_app/shared/app/extension/helper.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/logic/exchange_rate/exchange_rate_cubit.dart';
import 'package:gp_stock_app/shared/models/dropdown/dropdown_value.dart';
import 'package:gp_stock_app/shared/widgets/buttons/common_button.dart';
import 'package:gp_stock_app/shared/widgets/buttons/custom_radio_button.dart';
import 'package:gp_stock_app/shared/widgets/dropdown/input_dropdown.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

import 'apply_trial_contract_cubit.dart';
import 'trial_contract_summary_dialog.dart';

/// 申请 体验合约、彩金合约
/// Page for applying for experience contract  and bonus contract.
class ApplyTrialContractPage extends StatefulWidget {
  final ContractType contractType;
  final InstrumentType mainContractType;
  const ApplyTrialContractPage({
    super.key,
    required this.contractType,
    required this.mainContractType,
  });

  @override
  State<ApplyTrialContractPage> createState() => _ApplyTrialContractPageState();
}

class _ApplyTrialContractPageState extends State<ApplyTrialContractPage> {
  final amountController = TextEditingController();

  @override
  void initState() {
    super.initState();
    final rates = context.read<ExchangeRateCubit>().getCurrencyRateConfig('CNY');
    Helper.afterInit(
      () => context.read<ApplyTrialContractCubit>()
        ..getContractActivity(
            type: widget.contractType.value, exchangeRate: rates.rate, parentType: widget.mainContractType.value)
        ..setContractType(widget.mainContractType, widget.contractType),
    );
  }

  @override
  void dispose() {
    amountController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ApplyTrialContractCubit, ApplyTrialContractState>(
      listenWhen: (previous, current) => current.selectedAmount != previous.selectedAmount,
      listener: (context, state) {
        amountController.text = state.selectedAmount?.toStringAsFixed(0) ?? '';
      },
      child: Scaffold(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          surfaceTintColor: Colors.transparent,
          elevation: 0,
          iconTheme: IconThemeData(
            color: context.colorTheme.textPrimary,
          ),
          title: Text(
            '${'applyFor'.tr()} [${widget.contractType.title(widget.mainContractType).tr()}]',
          ),
        ),
        body: BlocBuilder<ApplyTrialContractCubit, ApplyTrialContractState>(
          builder: (context, state) {
            return SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.gw),
                child: Column(
                  children: [
                    _buildContractDetailsCard(context, state, widget.mainContractType),
                    12.verticalSpace,
                    ShadowBox(
                      child: _ContractDetailsSection(
                        contractType: widget.contractType,
                        amountController: amountController,
                      ),
                    ),
                    30.verticalSpace,
                    _SubmitSection(
                      state: state,
                      contractType: widget.contractType,
                      amountController: amountController,
                      mainContractType: widget.mainContractType,
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildContractDetailsCard(
      BuildContext context, ApplyTrialContractState state, InstrumentType mainContractType) {
    return ShadowBox(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _ContractTypeSection(
              contractActivity: state.contractConfig,
              mainContractType: widget.mainContractType,
              contractType: widget.contractType),
          const _Divider(),
          _PeriodSection(contractType: widget.contractType),
          const _Divider(),
          _LeverageSection(contractType: widget.contractType),
          const _Divider(),
          _AmountSection(contractType: widget.contractType),
        ],
      ),
    );
  }
}

class _ContractTypeSection extends StatelessWidget {
  final ApplyTrialContractConfigEntity? contractActivity;
  final ContractType contractType;
  final InstrumentType mainContractType;

  const _ContractTypeSection(
      {required this.contractActivity, required this.mainContractType, required this.contractType});

  @override
  Widget build(BuildContext context) {
    final hasApplyTrialContractConfigActivityRiskMap = contractActivity?.activityRiskMap.isNotEmpty == true;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'contractType'.tr(),
              style: context.textTheme.primary.fs16.w600,
            ),
            12.horizontalSpace,
            GestureDetector(
              onTap: () => _showContractInstructionDialog(context, contractType: contractType),
              child: Icon(
                Icons.help_outline,
                color: context.colorTheme.textPrimary,
              ),
            ),
          ],
        ),
        12.verticalSpace,
        SizedBox(
          height: 30.gh,
          child: mainContractType == InstrumentType.futures
              ? FutureFundingWidget()
              : hasApplyTrialContractConfigActivityRiskMap
                  ? _buildContractTypeList(context)
                  : _buildShimmerLoader(),
        ),
      ],
    );
  }

  Widget _buildContractTypeList(BuildContext context) {
    return ListView.separated(
      scrollDirection: Axis.horizontal,
      shrinkWrap: true,
      itemCount: contractActivity?.activityRiskMap.length ?? 0,
      separatorBuilder: (_, __) => 12.horizontalSpace,
      itemBuilder: (context, index) {
        final item = contractActivity?.activityRiskMap[index];
        if (item == null) return const SizedBox.shrink();

        return BlocSelector<ApplyTrialContractCubit, ApplyTrialContractState,
            (ApplyTrialContractConfigActivityRiskMap, String)>(
          selector: (state) => (
            state.selectedMarket ?? ApplyTrialContractConfigActivityRiskMap(),
            state.currency ?? '',
          ),
          builder: (context, selectedMarket) {
            final marketType = item.marketType;
            final title = contractMarketTranslation[marketType]?.tr() ?? '';
            return SelectableButton(
              title: title,
              isSelected: selectedMarket.$1.activityId == item.activityId,
              onTap: () {
                final newMarketCurrency = switch (item.marketType) {
                  'CN' => 'CNY',
                  'HK' => 'HKD',
                  'US' => 'USD',
                  _ => selectedMarket.$2,
                };
                final newRate = context.read<ExchangeRateCubit>().getCurrencyRateConfig(newMarketCurrency);
                context.read<ApplyTrialContractCubit>().selectMarket(item, newRate.rate);
              },
            );
          },
        );
      },
    );
  }

  Widget _buildShimmerLoader() {
    return ShimmerWidget(
      width: double.infinity,
      height: 30.gh,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.grey[300],
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  void _showContractInstructionDialog(BuildContext context, {required ContractType contractType}) {
    final selectedMarket = context.read<ApplyTrialContractCubit>().state.selectedMarket;
    final activityRule = selectedMarket?.activityRule;
    final hasInstructions = activityRule != null && activityRule.isNotEmpty;
    final contractTypeName = contractType.translationKey.tr();

    showDialog(
      context: context,
      builder: (context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16.gr),
          ),
          insetPadding: EdgeInsets.symmetric(horizontal: 24.gw),
          child: Container(
            width: 0.85.gsw,
            height: 0.6.gsh,
            padding: EdgeInsets.all(16.gw),
            decoration: BoxDecoration(
              color: context.theme.cardColor,
              borderRadius: BorderRadius.circular(16.gr),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '$contractTypeName ${'instructions'.tr()}',
                      style: context.textTheme.primary.fs16.w600,
                    ),
                    IconButton(
                      icon: Icon(
                        Icons.close,
                        color: context.colorTheme.textPrimary,
                        size: 20.gw,
                      ),
                      onPressed: () => Navigator.pop(context),
                      padding: EdgeInsets.zero,
                      constraints: BoxConstraints(),
                    ),
                  ],
                ),
                Divider(color: context.theme.dividerColor),
                8.verticalSpace,
                Expanded(
                  child: SingleChildScrollView(
                    physics: BouncingScrollPhysics(),
                    child: hasInstructions
                        ? Text(
                            activityRule,
                            style: context.textTheme.primary,
                          )
                        : Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.info_outline,
                                  size: 48.gw,
                                  color: context.colorTheme.textRegular.withAlpha(128),
                                ),
                                8.verticalSpace,
                                Text(
                                  'no_results_found'.tr(),
                                  style: context.textTheme.regular.w500,
                                ),
                              ],
                            ),
                          ),
                  ),
                ),
                8.verticalSpace,
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () => Navigator.pop(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: context.theme.primaryColor,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20.gr),
                      ),
                      padding: EdgeInsets.symmetric(vertical: 12.gh),
                    ),
                    child: Text(
                      'close'.tr(),
                      style: context.textTheme.buttonPrimary.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

class _AmountSection extends StatelessWidget {
  final ContractType contractType;

  const _AmountSection({required this.contractType});

  @override
  Widget build(BuildContext context) {
    return BlocSelector<
        ApplyTrialContractCubit,
        ApplyTrialContractState,
        (
          double?,
          List<double>,
          ContractModelAmount,
          ApplyTrialContractConfigActivityRiskMap,
          String,
          ApplyTrialContractConfigEntity?
        )>(
      selector: (state) => (
        state.selectedAmount,
        state.contractConfig?.activityRiskMap.first.applyAmountList ?? [],
        state.contractModelAmount ?? ContractModelAmount(),
        state.selectedMarket ?? ApplyTrialContractConfigActivityRiskMap(),
        state.currency ?? '',
        state.contractConfig,
      ),
      builder: (context, data) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AmountRow(
              title: 'giveDay'.tr(),
              value: '${data.$4.giveDay} ${'day'.tr()}',
            ),
            if (contractType == ContractType.bonus) ...[
              12.verticalSpace,
              AmountRow(
                title: 'bonusAmount'.tr(),
                amount: data.$4.giveAmount != 0 ? data.$4.giveAmount : ((data.$4.giveRatio) * (data.$1 ?? 0)) / 100,
                currency: data.$5,
              ),
              12.verticalSpace,
              AmountRow(
                title: 'availableBalance'.tr(),
                amount: data.$6?.useAmount ?? 0,
                currency: data.$6?.currency ?? '',
              ),
              12.verticalSpace,
              AmountRow(
                title: 'interestCoupon'.tr(),
                amount: data.$6?.interestCash ?? 0,
                currency: data.$6?.currency ?? '',
              ),
            ],
          ],
        );
      },
    );
  }
}

class _PeriodSection extends StatelessWidget {
  final ContractType contractType;

  const _PeriodSection({required this.contractType});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'period'.tr(),
          style: context.textTheme.primary.fs16.w500,
        ),
        12.verticalSpace,
        SizedBox(
          height: 30.gh,
          child:
              BlocSelector<ApplyTrialContractCubit, ApplyTrialContractState, ApplyTrialContractConfigActivityRiskMap?>(
            selector: (state) => state.selectedMarket,
            builder: (context, selectedMarket) {
              if (selectedMarket == null) return _buildShimmerLoader();
              return SelectableButton(
                title: 'contract.period_1'.tr(),
                isSelected: true,
                onTap: () {},
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildShimmerLoader() {
    return ShimmerWidget(
      width: double.infinity,
      height: 30.gh,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.grey[300],
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
}

class _LeverageSection extends StatelessWidget {
  final ContractType contractType;

  const _LeverageSection({required this.contractType});

  @override
  Widget build(BuildContext context) {
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Text(
        'leverage'.tr(),
        style: context.textTheme.primary.w600.fs16,
      ),
      12.verticalSpace,
      SizedBox(
        height: 30.gh,
        child: BlocSelector<ApplyTrialContractCubit, ApplyTrialContractState, ApplyTrialContractConfigActivityRiskMap?>(
          selector: (state) => state.selectedMarket,
          builder: (context, selectedMarket) {
            final multiple = selectedMarket?.multiple;
            if (multiple == null) return _buildShimmerLoader();

            return SelectableButton(
              title: '$multiple${'xTimes'.tr()}',
              isSelected: true,
              onTap: () {},
            );
          },
        ),
      ),
    ]);
  }

  Widget _buildShimmerLoader() {
    return ShimmerWidget(
      width: double.infinity,
      height: 30.gh,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.grey[300],
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
}

class _ContractDetailsSection extends StatelessWidget {
  final ContractType contractType;
  final TextEditingController amountController;
  const _ContractDetailsSection({required this.contractType, required this.amountController});

  @override
  Widget build(BuildContext context) {
    return BlocSelector<
        ApplyTrialContractCubit,
        ApplyTrialContractState,
        (
          double?,
          List<DropDownValue>,
          ContractModelAmount,
          ApplyTrialContractConfigActivityRiskMap,
          String,
          ApplyTrialContractConfigEntity?,
          ContractApplyAmountEntity?
        )>(
      selector: (state) => (
        state.selectedAmount,
        [],
        state.contractModelAmount ?? ContractModelAmount(),
        state.selectedMarket ?? ApplyTrialContractConfigActivityRiskMap(),
        state.currency ?? '',
        state.contractConfig,
        state.bonusContractCalculation,
      ),
      builder: (context, data) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (contractType == ContractType.bonus) ...[
              Text(
                'contractMargin'.tr(),
                style: context.textTheme.primary.w600.fs16,
              ),
              12.verticalSpace,
            ],
            if (contractType == ContractType.experience) ...[
              Text(
                'giftAmount'.tr(),
                style: context.textTheme.primary.w600.fs16,
              ),
              12.verticalSpace,
            ],
            contractType == ContractType.experience
                ? Container(
                    height: 48.gh,
                    decoration: BoxDecoration(
                      color: context.theme.inputDecorationTheme.fillColor,
                      borderRadius: BorderRadius.circular(8.gr),
                    ),
                    padding: EdgeInsets.symmetric(horizontal: 16.gw),
                    alignment: Alignment.centerLeft,
                    child: Text(
                      // Use giveAmount for experience contracts
                      '${data.$4.giveAmount.toStringAsFixed(2)} ${data.$5}',
                      style: context.textTheme.primary.w500.ffAkz,
                    ),
                  )
                : InputDropdownWidget<int>(
                    items: data.$4.applyAmountList.map((e) => e.toInt()).toList(),
                    textController: amountController,
                    itemBuilder: (dynamic item) =>
                        Center(child: Text(item.toStringAsFixed(0), style: context.textTheme.primary.fs16.ffAkz)),
                    onTextChanged: (value) {
                      if (value.isNotEmpty && int.tryParse(value) != null) {
                        final rates = context.read<ExchangeRateCubit>().getCurrencyRateConfig(data.$5);
                        context.read<ApplyTrialContractCubit>().selectAmount(double.tryParse(value), rates.rate);
                      }
                    },
                    onDropdownChanged: (value) {
                      if (value != null) {
                        final rates = context.read<ExchangeRateCubit>().getCurrencyRateConfig(data.$5);
                        context.read<ApplyTrialContractCubit>().selectAmount(value.toDouble(), rates.rate);
                      }
                    },
                  ),
            16.verticalSpace,
            _buildContractDetails(context, data, contractType),
          ],
        );
      },
    );
  }

  Widget _buildContractDetails(
      BuildContext context,
      (
        double?,
        List<DropDownValue>,
        ContractModelAmount,
        ApplyTrialContractConfigActivityRiskMap,
        String,
        ApplyTrialContractConfigEntity?,
        ContractApplyAmountEntity?
      ) data,
      ContractType contractType) {
    final amount = data.$3;
    final currency = data.$5;
    final contractCalculation = data.$7;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AmountRow(
          title: 'totalMargin'.tr(),
          amount: amount.totalTadingFunds ?? 0, // Note: There's a typo in the API field name
          currency: currency,
        ),
        12.verticalSpace,
        AmountRow(
          title: 'lossWarningLine'.tr(),
          amount: amount.lossWarningLine ?? 0,
          currency: currency,
        ),
        12.verticalSpace,
        AmountRow(
          title: 'liquidationLine'.tr(),
          amount: amount.lossFlatLine ?? 0,
          currency: currency,
        ),
        if (contractType == ContractType.bonus) ...[
          12.verticalSpace,
          AmountRow(
            title: 'capitalInterestRate'.tr(),
            amount: contractCalculation?.rateAmount ?? 0,
            currency: '$currency/${'day'.tr()}',
          ),
          12.verticalSpace,
          AmountRow(
            title: 'interestReduceAmount'.tr(),
            amount: contractCalculation?.deductInterestCashCNY ?? 0,
            isCurrency: true,
            currency: currency,
          ),
          12.verticalSpace,
          AmountRow(
            title: 'actualPaymentAmount2'.tr(),
            amount: contractCalculation?.deductCanUseCashCNY ?? 0,
            isCurrency: true,
            prefix: currency != 'CNY' ? '≈ ' : '',
            currency: 'CNY',
          ),
        ]
      ],
    );
  }
}

/// Section for agreement checkbox and submit button
class _SubmitSection extends StatelessWidget {
  final ApplyTrialContractState state;
  final ContractType contractType;
  final InstrumentType mainContractType;
  final TextEditingController amountController;
  const _SubmitSection({
    required this.state,
    required this.contractType,
    required this.mainContractType,
    required this.amountController,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildAgreementRow(context),
        9.verticalSpace,
        CommonButton(
          enable: state.isAgree,
          onPressed: () => _showContractSummaryDialog(context, state: state),
          title: 'verify'.tr(),
        ),
        37.verticalSpace,
      ],
    );
  }

  Widget _buildAgreementRow(BuildContext context) {
    final periods = {
      1: 1,
      2: 7,
      3: 30,
    };
    return Row(
      children: [
        CustomRadioButton(
          isSelected: state.isAgree,
          onChange: (value) => context.read<ApplyTrialContractCubit>().updateIsAgree(value),
        ),
        9.horizontalSpace,
        Text.rich(
          TextSpan(
            text: '${'readAndAgree'.tr()} ',
            style: context.textTheme.regular.fs10,
            children: [
              TextSpan(
                text: 'marginAgreement'.tr(),
                style: context.textTheme.primary.w500.fs10,
                recognizer: TapGestureRecognizer()
                  ..onTap = () {
                    showAppInfoBottomSheet(
                      context,
                      capitalA: '${((state.selectedMarket?.multiple ?? 0) + 1) * (state.selectedAmount ?? 1)}',
                      capitalB: '${state.selectedAmount ?? 1}',
                      interestRate: '${state.bonusContractCalculation?.rate ?? 1}',
                      interestAmount: '${state.bonusContractCalculation?.rateAmount ?? 1}',
                      period: 1,
                    );
                  },
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _showContractSummaryDialog(
    BuildContext context, {
    required ApplyTrialContractState state,
  }) {
    // if ((int.tryParse(amountController.text.trim()) ?? 0) < 500) {
    //   GPEasyLoading.showToast('minimumAmountHint'.tr());
    //   return;
    // }
    showDialog(
      context: context,
      builder: (_) => BlocProvider.value(
        value: context.read<ApplyTrialContractCubit>(),
        child: TrialContractSummaryDialog(
          contractType: contractType,
          mainContractType: mainContractType,
        ),
      ),
    );
  }
}

class _Divider extends StatelessWidget {
  const _Divider();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 16.gh),
      child: Divider(
        color: Colors.grey.withNewOpacity(0.1),
        height: 1,
      ),
    );
  }
}
