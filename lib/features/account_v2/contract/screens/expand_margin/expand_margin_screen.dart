import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/models/entities/contract/contract.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/contract/domain/models/contract_model.dart';
import 'package:gp_stock_app/features/contract/widgets/amount_row.dart';
import 'package:gp_stock_app/features/contract/widgets/app_info_bottom_sheet.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/logic/account_info/account_info_cubit.dart';
import 'package:gp_stock_app/shared/logic/account_info/account_info_state.dart';
import 'package:gp_stock_app/shared/logic/exchange_rate/exchange_rate_cubit.dart';
import 'package:gp_stock_app/shared/widgets/buttons/custom_material_button.dart';
import 'package:gp_stock_app/shared/widgets/buttons/custom_radio_button.dart';
import 'package:gp_stock_app/features/contract/widgets/selectable_button.dart';
import 'package:gp_stock_app/shared/widgets/dropdown/input_dropdown.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

import 'expand_margin_cubit.dart';

import 'summary_dialog.dart';

/// 扩大保证金和追加保证金（补充损失）页面
/// Page for both expanding margin and replenishing amount (supplement loss)
class ExpandMarginScreen extends StatefulWidget {
  const ExpandMarginScreen({super.key});

  @override
  State<ExpandMarginScreen> createState() => _ExpandMarginScreenState();
}

class _ExpandMarginScreenState extends State<ExpandMarginScreen> {
  final amountController = TextEditingController();

  @override
  void dispose() {
    amountController.dispose();
    super.dispose();
  }

  String _getScreenTitle() {
    final state = context.read<ExpandMarginCubit>().state;
    return switch (state.contractActionType) {
      ContractAction.replenish => 'supplementLoss'.tr(),
      ContractAction.renew => 'contractRenewal'.tr(),
      ContractAction.marginExpand => 'expandMargin'.tr(),
    };
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ExpandMarginCubit, ExpandMarginState>(
      listenWhen: (previous, current) => previous.selectedAmount != current.selectedAmount,
      listener: (context, state) {
        amountController.text = state.selectedAmount?.toString() ?? '';
      },
      child: Scaffold(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          surfaceTintColor: Colors.transparent,
          elevation: 0,
          iconTheme: IconThemeData(
            color: context.colorTheme.textPrimary,
          ),
          title: Text(
            _getScreenTitle(),
            style: context.textTheme.primary.fs16.w500,
          ),
          centerTitle: true,
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.gw),
            child: BlocBuilder<ExpandMarginCubit, ExpandMarginState>(
              builder: (context, state) {
                final marginCallData = state.marginCallResponse;
                return Column(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: context.theme.cardColor,
                        borderRadius: BorderRadius.circular(8.gr),
                      ),
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 14.gw, vertical: 12.gh),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _ContractTypeSection(marginCallData: marginCallData),
                            _Divider(),
                            _PeriodSection(marginCallData: marginCallData),
                            _Divider(),
                            _LeverageSection(marginCallData: marginCallData),
                            _Divider(),
                            _AmountSection(
                              marginCallData: marginCallData,
                              contractActionType: state.contractActionType,
                            ),
                            _Divider(),
                            _ContractDetailsSection(
                              contractActionType: state.contractActionType,
                              amountController: amountController,
                              contractType: state.contractType,
                            ),
                            // 16.verticalSpace,
                            // _TotalPaymentSection(),
                          ],
                        ),
                      ),
                    ),
                    30.verticalSpace,
                    _SubmitSection(
                      contractActionType: state.contractActionType,
                      state: state,
                      amountController: amountController,
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}

class _ContractTypeSection extends StatelessWidget {
  final ContractMarginEntity? marginCallData;

  const _ContractTypeSection({required this.marginCallData});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'contractType'.tr(),
          style: context.textTheme.primary.fs16.w600,
        ),
        12.verticalSpace,
        marginCallData != null
            ? SizedBox(
                height: 30.gh,
                child: SelectableButton(
                  title: marginCallData?.contractTypeText?.tr() ?? '',
                  isSelected: true,
                  onTap: () {},
                ),
              )
            : ShimmerWidget(
                width: double.infinity,
                height: 30.gh,
              ),
      ],
    );
  }
}

class _PeriodSection extends StatelessWidget {
  final ContractMarginEntity? marginCallData;

  const _PeriodSection({required this.marginCallData});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'period'.tr(),
          style: context.textTheme.primary.fs16.w600,
        ),
        12.verticalSpace,
        marginCallData != null
            ? SizedBox(
                height: 30.gh,
                child: SelectableButton(
                  title: marginCallData?.periodTypeText?.tr() ?? '',
                  isSelected: true,
                  onTap: () {},
                ),
              )
            : ShimmerWidget(
                width: double.infinity,
                height: 30.gh,
              ),
      ],
    );
  }
}

class _LeverageSection extends StatelessWidget {
  final ContractMarginEntity? marginCallData;

  const _LeverageSection({required this.marginCallData});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'contractMultiple'.tr(),
          style: context.textTheme.primary.fs16.w600,
        ),
        12.verticalSpace,
        marginCallData != null
            ? SizedBox(
                height: 30.gh,
                child: SelectableButton(
                  title: '${marginCallData?.multiple ?? 0}${'xTimes'.tr()}',
                  isSelected: true,
                  onTap: () {},
                ),
              )
            : ShimmerWidget(
                width: double.infinity,
                height: 30.gh,
              ),
      ],
    );
  }
}

class _AmountSection extends StatelessWidget {
  final ContractMarginEntity? marginCallData;
  final ContractAction contractActionType;

  const _AmountSection({required this.marginCallData, required this.contractActionType});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AccountInfoCubit, AccountInfoState>(
      builder: (context, state) {
        return Column(
          children: [
            AmountRow(
              title: 'availableBalance'.tr(),
              amount: state.accountInfo?.usableCash ?? 0,
              currency: state.accountInfo?.currency ?? '',
            ),
            // Only show interest coupon for renew type
            if (contractActionType == ContractAction.renew) ...[
              12.verticalSpace,
              AmountRow(
                title: 'interestCoupon'.tr(),
                amount: marginCallData?.interestRate ?? 0,
                currency: 'CNY',
              ),
            ],
          ],
        );
      },
    );
  }
}

class _ContractDetailsSection extends StatelessWidget {
  final ContractAction contractActionType;
  final ContractType contractType;
  final TextEditingController amountController;

  const _ContractDetailsSection(
      {required this.contractActionType, required this.amountController, required this.contractType});

  String _getSectionTitle() {
    return switch (contractActionType) {
      ContractAction.replenish => 'replenishAmount'.tr(),
      ContractAction.renew => 'contractRenewal'.tr(),
      ContractAction.marginExpand => 'contractMargin'.tr(),
    };
  }

  // Map marketType to currency code
  String getCurrency(String? marketType) {
    return switch (marketType) {
      "CN" => "CNY",
      "HK" => "HKD",
      "US" => "USD",
      _ => "CNY" // Default
    };
  }

  @override
  Widget build(BuildContext context) {
    return BlocSelector<ExpandMarginCubit, ExpandMarginState, (int?, ContractMarginEntity?, ContractModelAmount?)>(
      selector: (state) => (
        state.selectedAmount,
        state.marginCallResponse,
        state.contractModelAmount,
      ),
      builder: (context, state) {
        final (selectedAmount, marginCallData, contractModel) = state;
        List<double>? bonusAmountList;
        if (marginCallData?.contractType == ContractType.bonus) {
          bonusAmountList = marginCallData?.bonusAmountList ?? [];
        }
        final amountList = marginCallData?.amountList ?? [];
        final currency = getCurrency(marginCallData?.marketType);
        final principal = state.$1 ?? 0;
        final rate = context.read<ExchangeRateCubit>().getCurrencyRateConfig(currency).rate;
        // final percent =
        //     contractType == Contract.marginCall ? marginCallData?.totalFinance ?? 0 : contractModel.multipleAmount;
        final totalPay = contractActionType == ContractAction.replenish
            ? (principal / rate)
            : (principal + (contractModel?.interestRate ?? 0)) / rate;
        amountController.text = selectedAmount?.toString() ?? '';
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _getSectionTitle(),
              style: context.textTheme.primary.fs16.w600,
            ),
            12.verticalSpace,
            if (contractType == ContractType.bonus)
              InputDropdownWidget<double>(
                items: bonusAmountList ?? [],
                textController: amountController..text = selectedAmount?.toString() ?? '',
                itemBuilder: (dynamic item) =>
                    Center(child: Text(item.toStringAsFixed(0), style: context.textTheme.primary.fs16.ffAkz)),
                onTextChanged: (value) {
                  // if (value.isNotEmpty && int.tryParse(value) != null && int.parse(value) >= 500) {
                  context.read<ExpandMarginCubit>().setSelectedAmount(int.tryParse(value));
                  // }
                },
                onDropdownChanged: (value) {
                  context.read<ExpandMarginCubit>().setSelectedAmount(value?.toInt() ?? 0);
                },
              )
            else
              InputDropdownWidget<ContractMarginAmountList>(
                items: amountList.map((e) => e).toList(),
                textController: amountController..text = selectedAmount?.toString() ?? '',
                itemBuilder: (dynamic item) => Center(
                    child: Text(item.applyAmount.toStringAsFixed(0), style: context.textTheme.primary.fs16.ffAkz)),
                onTextChanged: (value) {
                  // if (value.isNotEmpty && int.tryParse(value) != null && int.parse(value) >= 500) {
                  context.read<ExpandMarginCubit>().setSelectedAmount(int.tryParse(value));
                  // }
                },
                onDropdownChanged: (value) {
                  context.read<ExpandMarginCubit>().setSelectedAmount(value?.applyAmount.toInt() ?? 0);
                },
              ),
            12.verticalSpace,
            AmountRow(
              title: 'totalMargin'.tr(),
              amount: contractActionType == ContractAction.replenish
                  ? marginCallData?.totalPower ?? 0
                  : contractModel?.totalTadingFunds ?? 0,
              currency: currency,
            ),
            12.verticalSpace,
            AmountRow(
              title: 'lossWarningLine'.tr(),
              amount: contractActionType == ContractAction.replenish
                  ? marginCallData?.warnAmount ?? 0
                  : contractModel?.lossWarningLine ?? 0,
              currency: currency,
            ),
            12.verticalSpace,
            AmountRow(
              title: 'liquidationLine'.tr(),
              amount: contractActionType == ContractAction.replenish
                  ? marginCallData?.closeAmount ?? 0
                  : contractModel?.lossFlatLine ?? 0,
              currency: currency,
            ),
            if (contractActionType != ContractAction.replenish) ...[
              12.verticalSpace,
              AmountRow(
                title: 'capitalInterestRate'.tr(),
                amount: contractModel?.interestRate ?? 0,
                currency: '$currency${marginCallData != null ? 'period_${marginCallData.periodType}_rate'.tr() : ''}',
              ),
            ],
            12.verticalSpace,
            AmountRow(
              title: 'actualPaymentAmount'.tr(),
              amount: totalPay,
              currency: 'CNY',
              prefix: currency != 'CNY' ? '≈ ' : '',
            ),
            12.verticalSpace,
          ],
        );
      },
    );
  }
}

class _SubmitSection extends StatelessWidget {
  final ContractAction contractActionType;
  final ExpandMarginState state;
  final TextEditingController amountController;

  const _SubmitSection({
    required this.contractActionType,
    required this.state,
    required this.amountController,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            CustomRadioButton(
              isSelected: state.isAgree,
              onChange: (value) => context.read<ExpandMarginCubit>().setIsAgree(value),
            ),
            9.horizontalSpace,
            Text.rich(
              TextSpan(
                text: '${'readAndAgree'.tr()} ',
                style: context.textTheme.regular.fs10,
                children: [
                  TextSpan(
                    text: 'marginAgreement'.tr(),
                    style: context.textTheme.primary.w500.fs10,
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        showAppInfoBottomSheet(
                          context,
                          capitalA: '10000',
                          capitalB: '10000',
                          interestRate: '10',
                          interestAmount: '1000',
                          period: 1,
                        );
                      },
                  ),
                ],
              ),
            ),
          ],
        ),
        9.verticalSpace,
        CustomMaterialButton(
          isEnabled: state.isAgree,
          onPressed: () {
            // if ((int.tryParse(amountController.text.trim()) ?? 0) < 500) {
            //   GPEasyLoading.showToast('minimumAmountHint'.tr());
            //   return;
            // }
            showDialog(
              context: context,
              builder: (_) => MultiBlocProvider(
                providers: [
                  BlocProvider.value(
                    value: context.read<ExpandMarginCubit>(),
                  ),
                  BlocProvider.value(
                    value: context.read<ExchangeRateCubit>(),
                  ),
                ],
                child: SummaryDialog(contractType: contractActionType),
              ),
            );
          },
          buttonText: 'confirm'.tr(),
          textColor: Colors.white,
          borderRadius: 8,
        ),
        37.verticalSpace,
      ],
    );
  }
}

class _Divider extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 16.gh),
      child: Divider(
        color: Colors.grey.withValues(alpha: 0.1),
        height: 1,
      ),
    );
  }
}
